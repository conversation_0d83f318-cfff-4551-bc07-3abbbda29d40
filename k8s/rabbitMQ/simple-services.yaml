apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-headless
  namespace: amx-middleware
  labels:
    app: rabbitmq
spec:
  clusterIP: None
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
  - name: management
    port: 15672
    targetPort: 15672
  - name: epmd
    port: 4369
    targetPort: 4369
  selector:
    app: rabbitmq
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq
  namespace: amx-middleware
  labels:
    app: rabbitmq
spec:
  type: ClusterIP
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
  - name: management
    port: 15672
    targetPort: 15672
  selector:
    app: rabbitmq
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-management
  namespace: amx-middleware
  labels:
    app: rabbitmq
spec:
  type: NodePort
  ports:
  - name: management
    port: 15672
    targetPort: 15672
    nodePort: 30672
  selector:
    app: rabbitmq
