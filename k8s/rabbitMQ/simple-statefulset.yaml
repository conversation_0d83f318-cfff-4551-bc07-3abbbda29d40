apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rabbitmq
  namespace: amx-middleware
spec:
  serviceName: rabbitmq-headless
  replicas: 3
  selector:
    matchLabels:
      app: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
    spec:
      terminationGracePeriodSeconds: 60
      containers:
        - name: rabbitmq
          image: dockerhub.test.wacai.info/wse/rabbitmq-delayed-message-exchange:4.1.1-management
          ports:
            - name: amqp
              containerPort: 5672
            - name: management
              containerPort: 15672
            - name: epmd
              containerPort: 4369
          env:
            - name: K8S_SERVICE_NAME
              value: rabbitmq-headless
            - name: RABBITMQ_USE_LONGNAME
              value: "true"
            - name: RABBITMQ_NODENAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: RABBITMQ_ERLANG_COOKIE
              value: "rabbitmq-cluster-cookie"
          volumeMounts:
            - name: rabbitmq-storage
              mountPath: /var/lib/rabbitmq
            - name: rabbitmq-plugins
              mountPath: /etc/rabbitmq/enabled_plugins
              subPath: enabled_plugins
            - name: rabbitmq-config
              mountPath: /etc/rabbitmq/rabbitmq.conf
              subPath: rabbitmq.conf

          livenessProbe:
            exec:
              command: ["rabbitmq-diagnostics", "status"]
            initialDelaySeconds: 60
            periodSeconds: 60
          readinessProbe:
            exec:
              command: ["rabbitmq-diagnostics", "ping"]
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
      volumes:
        - name: rabbitmq-plugins
          configMap:
            name: rabbitmq-config
        - name: rabbitmq-config
          configMap:
            name: rabbitmq-config
  updateStrategy:
    type: RollingUpdate
  volumeClaimTemplates:
    - metadata:
        name: rabbitmq-storage
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: local-static
        resources:
          requests:
            storage: 10Gi