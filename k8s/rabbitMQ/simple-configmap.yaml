apiVersion: v1
kind: ConfigMap
metadata:
  name: rabbitmq-config
  namespace: amx-middleware
data:
  enabled_plugins: |
    [rabbitmq_management,rabbitmq_peer_discovery_k8s,rabbitmq_delayed_message_exchange].
  rabbitmq.conf: |
    # Cluster formation
    cluster_formation.peer_discovery_backend = rabbit_peer_discovery_k8s
    cluster_formation.k8s.host = kubernetes.default.svc.cluster.local
    cluster_formation.k8s.address_type = hostname
    cluster_formation.node_cleanup.interval = 30
    cluster_formation.node_cleanup.only_log_warning = true
    cluster_partition_handling = autoheal
    
    # Queue master locator
    queue_master_locator = min-masters
    
  
    ## Networking
    loopback_users.guest = false
    listeners.tcp.default = 5672
    
    ## Memory
    vm_memory_high_watermark.absolute = 2GB
