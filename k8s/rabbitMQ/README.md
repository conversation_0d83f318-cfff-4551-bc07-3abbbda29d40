# RabbitMQ 4.x 集群部署 - 简化版

这是一个简化版的RabbitMQ 4.x集群部署方案，包含以下特性：

## 功能特性

- **RabbitMQ 4.x**: 使用最新的RabbitMQ 4.0版本
- **集群模式**: 3节点StatefulSet集群
- **必需插件**: 
  - `rabbitmq_management`: 管理界面
  - `rabbitmq_delayed_message_exchange`: 延迟消息交换器
  - `rabbitmq_peer_discovery_k8s`: Kubernetes集群发现
- **无认证**: 使用默认的guest/guest用户，简化配置
- **持久化存储**: 每个节点8GB存储

## 部署文件说明

- `simple-rbac.yaml`: RBAC权限配置，用于Kubernetes集群发现
- `simple-configmap.yaml`: RabbitMQ配置和插件列表
- `simple-statefulset.yaml`: RabbitMQ集群StatefulSet
- `simple-services.yaml`: 服务配置（内部访问和NodePort管理界面）
- `deploy.sh`: 一键部署脚本

## 快速部署

```bash
# 进入目录
cd k8s/rabbitMQ

# 执行部署脚本
chmod +x deploy.sh
./deploy.sh
```

## 手动部署

```bash
# 1. 创建命名空间（如果不存在）
kubectl create namespace amx-middleware

# 2. 按顺序部署
kubectl apply -f simple-rbac.yaml
kubectl apply -f simple-configmap.yaml
kubectl apply -f simple-statefulset.yaml
kubectl apply -f simple-services.yaml
```

## 访问方式

### AMQP连接
- **内部访问**: `rabbitmq.amx-middleware.svc.cluster.local:5672`
- **用户名/密码**: `guest/guest`

### 管理界面
- **外部访问**: `http://<node-ip>:30672`
- **用户名/密码**: `guest/guest`

## 验证部署

```bash
# 检查Pod状态
kubectl get pods -n amx-middleware -l app=rabbitmq

# 检查集群状态
kubectl exec -it rabbitmq-0 -n amx-middleware -- rabbitmq-diagnostics cluster_status

# 检查插件状态
kubectl exec -it rabbitmq-0 -n amx-middleware -- rabbitmq-plugins list

# 查看日志
kubectl logs rabbitmq-0 -n amx-middleware
```

## 使用延迟消息插件

延迟消息插件已启用，可以创建延迟交换器：

```bash
# 进入任意Pod
kubectl exec -it rabbitmq-0 -n amx-middleware -- bash

# 创建延迟交换器
rabbitmqadmin declare exchange name=delayed-exchange type=x-delayed-message arguments='{"x-delayed-type":"direct"}'
```

## 清理部署

```bash
kubectl delete -f simple-services.yaml
kubectl delete -f simple-statefulset.yaml
kubectl delete -f simple-configmap.yaml
kubectl delete -f simple-rbac.yaml

# 删除PVC（可选，会删除数据）
kubectl delete pvc -l app=rabbitmq -n amx-middleware
```

## 注意事项

1. **安全性**: 此配置禁用了认证，仅适用于开发/测试环境
2. **存储**: 默认使用8GB存储，根据需要调整
3. **资源**: 每个Pod默认512Mi内存，生产环境建议增加
4. **网络**: 使用NodePort 30672访问管理界面，确保端口未被占用
