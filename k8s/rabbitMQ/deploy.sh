#!/bin/bash

# 简化版RabbitMQ集群部署脚本
# 使用默认的guest/guest用户，无需额外认证

echo "部署RabbitMQ集群到amx-middleware命名空间..."

# 确保命名空间存在
kubectl create namespace amx-middleware --dry-run=client -o yaml | kubectl apply -f -

# 按顺序部署资源
echo "1. 部署RBAC配置..."
kubectl apply -f simple-rbac.yaml

echo "2. 部署ConfigMap..."
kubectl apply -f simple-configmap.yaml

echo "3. 部署StatefulSet..."
kubectl apply -f simple-statefulset.yaml

echo "4. 部署Services..."
kubectl apply -f simple-services.yaml

echo "等待Pod启动..."
kubectl wait --for=condition=ready pod -l app=rabbitmq -n amx-middleware --timeout=300s

echo "部署完成！"
echo ""
echo "访问信息："
echo "- AMQP端口: rabbitmq.amx-middleware.svc.cluster.local:5672"
echo "- 管理界面: http://<node-ip>:30672"
echo "- 默认用户名/密码: guest/guest"
echo ""
echo "检查集群状态："
echo "kubectl exec -it rabbitmq-0 -n amx-middleware -- rabbitmq-diagnostics cluster_status"
