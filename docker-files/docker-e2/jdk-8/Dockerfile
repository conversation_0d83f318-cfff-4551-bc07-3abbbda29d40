FROM dockerhub.test.wacai.info/library/centos:6.8

ARG JDK_TAR_GZ="jdk-*.tar.gz"
ENV JDK_VERSION 1.8.0_291

COPY ${JDK_TAR_GZ} .
RUN tar -xvf ${JDK_TAR_GZ} && \
  rm -rf ${JDK_TAR_GZ} && \
  mv jdk${JDK_VERSION} /data/program/java && \
  chown -R appweb:appweb /data/program/java

ENV JAVA_HOME /data/program/java
ENV PATH $JAVA_HOME/bin:$PATH
ENV CLASSPATH .:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar

RUN sed -i '<EMAIL>=file:/dev/<EMAIL>=file:/dev/urandom@' \
    $JAVA_HOME/jre/lib/security/java.security%