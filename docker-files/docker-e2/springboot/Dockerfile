FROM dockerhub.test.wacai.info/e2pub/jdk:1.8.0-291

VOLUME /data/k2/log

COPY config /config
COPY target/${artifactId}*.jar ${workDir}/lib/${artifactId}-${versionForPkg}.jar
COPY run-java.sh setenv.sh plugin.sh ${workDir}/bin/

RUN mkdir /logback && \
    mv /config/logback.xml /logback

ENV JAVA_APP_JAR=${workDir}/lib/${artifactId}-${versionForPkg}.jar
ENV GROUP=${groupId} ARTIFACT=${artifactId} VERSION=${versionForPkg}
WORKDIR ${workDir}
CMD [ "bin/run-java.sh" ]