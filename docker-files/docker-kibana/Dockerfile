FROM --platform=linux/amd64 centos:7 AS builder

ARG KIBANA_TGZ="kibana-7.11.2-linux-x86_64.tar.gz"
ARG KIBANA_NAME="kibana-7.11.2-linux-x86_64"
ARG KIBANA_CONFIG="kibana.yml"

ENV APP_DIR /data/program/kibana

#创建用户，不能通过root启动
RUN useradd -m -d /home/<USER>/bin/bash appweb

COPY ${KIBANA_TGZ} .
RUN tar -xvf ${KIBANA_TGZ} && \
    rm -rf ${KIBANA_TGZ} && \
    mkdir -p /data/program && \
    mv ${KIBANA_NAME} /data/program/kibana && \
    chown -R appweb:appweb /data/program/kibana

COPY ${KIBANA_CONFIG} /data/program/kibana/config

# sets the working directory
WORKDIR $APP_DIR

USER appweb

EXPOSE 5601

CMD /bin/bash -c "/data/program/kibana/bin/kibana"

