FROM dockerhub.test.wacai.info/e2pub/jdk:11.0.13.1663233447

ARG CMAK_ZIP="cmak-*.zip"
ARG CMAK_VERSION="*******"

ENV APP_DIR /data/program/cmak

COPY ${CMAK_ZIP} .
RUN unzip ${CMAK_ZIP} && \
  rm -rf ${CMAK_ZIP} && \
  mv cmak-${CMAK_VERSION} /data/program/cmak && \
  chown -R appweb:appweb /data/program/cmak

# sets the working directory
WORKDIR $APP_DIR

EXPOSE 8080
ENV JAVA_OPTS " -Xmx1g -Xms1g "
CMD /bin/bash -c "/data/program/cmak/bin/cmak -Dapplication.home=/data/program/cmak -Dconfig.file=conf/application.conf -Dhttp.port=8080"

