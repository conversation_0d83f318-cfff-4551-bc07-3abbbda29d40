FROM dockerhub.test.wacai.info/e2pub/jdk:1.8.0-291
#FROM centos-jdk:1.8

RUN yum install -y \
        telnet \
        traceroute \
        iproute \
        iptables \
        net-tools \
        tcpdump \
        which

#安装包
ARG ES_TAR_GZ="elasticsearch-7.11.2-linux-x86_64.tar.gz"
ARG ES_NAME="elasticsearch-7.11.2"

## 配置文件
ARG ES_CONFIG="elasticsearch.yml"
ARG JAVA_OPTIONS="jvm.options"
ARG ES_BIN="elasticsearch"

#创建用户，ES不能通过root启动
RUN useradd -m -d /home/<USER>/bin/bash elasticsearch

COPY ${ES_TAR_GZ} .
RUN tar -xvf ${ES_TAR_GZ} && \
    rm -rf ${ES_TAR_GZ} && \
    mkdir -p /data/program && \
    mv ${ES_NAME} /data/program/elasticsearch && \
    chown -R elasticsearch:elasticsearch /data/program/elasticsearch && \
    find . -name "._*" | xargs rm -rf


#系统设置
# RUN echo 'vm.max_map_count=500000' >> /etc/sysctl.conf && \
#  sysctl -p  && \
#  echo 'elasticsearch soft memlock unlimited' >> /etc/security/limits.conf && \
#  echo 'elasticsearch hard memlock unlimited' >> /etc/security/limits.conf

#配置
COPY ${ES_CONFIG} /data/program/elasticsearch/config
COPY ${ES_BIN} /data/program/elasticsearch/bin
#修改权限
RUN chown elasticsearch:elasticsearch /data/program/elasticsearch/bin/elasticsearch

USER elasticsearch
RUN mkdir -p /data/program/elasticsearch/data && \
    mkdir -p /data/program/elasticsearch/logs && \
    chmod u+x /data/program/elasticsearch/bin/elasticsearch


EXPOSE 9200 9300

#启动
#CMD ["/bin/bash"]
CMD /bin/bash -c "/data/program/elasticsearch/bin/elasticsearch"

