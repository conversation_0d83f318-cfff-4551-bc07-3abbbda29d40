cluster.name: ${CLUSTER_NAME}
node.name: ${HOSTNAME}
node.attr.rack: r1
node.master: false
node.data: false
http.port: 9200

thread_pool.search.size: 200
node.max_local_storage_nodes: 3
http.compression: true
indices.queries.cache.size: 20%
cluster.routing.allocation.same_shard.host: true
bootstrap.memory_lock: false
network.host: 0.0.0.0

discovery.seed_hosts: ${UNICAST_HOSTS}
cluster.initial_master_nodes: ${UNICAST_HOSTS}

gateway.recover_after_nodes: 1
bootstrap.system_call_filter: false

xpack.security.enabled: false
xpack.ml.enabled: false

path.data: /data/program/elasticsearch/data
path.logs: /data/program/elasticsearch/logs

