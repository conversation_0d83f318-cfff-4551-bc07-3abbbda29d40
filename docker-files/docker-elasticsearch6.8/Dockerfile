FROM dockerhub.test.wacai.info/e2pub/jdk:1.8.0-291
#FROM centos-jdk:1.8

RUN yum install -y \
        telnet \
        traceroute \
        iproute \
        iptables \
        net-tools \
        tcpdump \
        which

#安装包
ARG ES_TAR_GZ="elasticsearch-6.8.23.tgz"
ARG ES_NAME="elasticsearch-6.8.23"

## 配置文件
ARG ES_CONFIG="elasticsearch.yml"
ARG JAVA_POLIC="java.policy"

#创建用户，ES不能通过root启动
RUN useradd -m -d /home/<USER>/bin/bash elasticsearch

COPY ${ES_TAR_GZ} .
RUN tar -xvf ${ES_TAR_GZ} && \
    rm -rf ${ES_TAR_GZ} && \
    mkdir -p /data/program && \
    mv ${ES_NAME} /data/program/elasticsearch && \
    chown -R elasticsearch:elasticsearch /data/program/elasticsearch && \
    find . -name "._*" | xargs rm -rf


#系统设置
# RUN echo 'vm.max_map_count=500000' >> /etc/sysctl.conf && \
#  sysctl -p  && \
#  echo 'elasticsearch soft memlock unlimited' >> /etc/security/limits.conf && \
#  echo 'elasticsearch hard memlock unlimited' >> /etc/security/limits.conf


USER elasticsearch

RUN mkdir -p /data/program/elasticsearch/data && \
    mkdir -p /data/program/elasticsearch/logs && \
    mkdir -p /data/program/elasticsearch/config && \
    echo '-Djava.security.policy=file:///data/program/elasticsearch/config/java.policy' >> /data/program/elasticsearch/config/jvm.options


#配置
COPY ${ES_CONFIG} /data/program/elasticsearch/config
COPY ${JAVA_POLIC} /data/program/elasticsearch/config

EXPOSE 9200 9300

#启动
#CMD ["/bin/bash"]
CMD /bin/bash -c "/data/program/elasticsearch/bin/elasticsearch"

