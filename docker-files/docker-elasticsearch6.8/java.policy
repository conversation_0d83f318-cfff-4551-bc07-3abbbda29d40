grant codeBase "file:${user.home}/.arthas/lib/-" {
    permission java.io.FilePermission "${user.home}/.arthas/-", "read,write";
    permission java.io.FilePermission "${user.home}/logs/arthas/-", "read,write,delete";
    permission java.lang.RuntimePermission "createClassLoader";
    permission java.lang.RuntimePermission "getClassLoader";
    permission java.lang.RuntimePermission "modifyThreadGroup";
    permission java.lang.RuntimePermission "modifyThread";
    permission java.lang.RuntimePermission "shutdownHooks";
    permission java.lang.RuntimePermission "accessClassInPackage.sun.reflect";
    permission java.lang.RuntimePermission "accessClassInPackage.sun.net.www.protocol.http";
    permission java.lang.RuntimePermission "accessClassInPackage.sun.net.www.http";
    permission java.net.SocketPermission "127.0.0.1:3658", "listen,resolve";
    permission java.net.SocketPermission "127.0.0.1:8563", "listen,resolve";
    permission java.net.SocketPermission "*", "accept";
};

grant {
    permission org.elasticsearch.ThreadPermission "modifyArbitraryThreadGroup";
    permission org.elasticsearch.ThreadPermission "modifyArbitraryThread";
};


grant {
    permission java.io.FilePermission "<<ALL FILES>>", "read,write";
    permission java.util.PropertyPermission "JM.LOG.PATH", "write";
    permission java.lang.RuntimePermission "*";
    permission java.lang.reflect.ReflectPermission "*";
    permission java.net.SocketPermission "*", "connect,listen,resolve,accept";
    permission ognl.OgnlInvokePermission "*";
};

grant {

    permission java.security.AllPermission;
    permission java.lang.RuntimePermission "*";
    permission java.lang.reflect.ReflectPermission "suppressAccessChecks";
    permission java.net.NetPermission "*";
    permission java.net.SocketPermission "localhost:0","resolve,listen";
    permission java.net.URLPermission "http:*","*:*";
    permission java.net.URLPermission "https:*","*:*";
    permission org.elasticsearch.ThreadPermission "modifyArbitraryThreadGroup";
};
