cluster.name: ${CLUSTER_NAME}
node.name: ${HOSTNAME}
node.attr.rack: r1
node.master: false
node.data: false
http.enabled: true
http.port: 9200
thread_pool.bulk.queue_size: 50000
thread_pool.search.size: 200
node.max_local_storage_nodes: 3

http.compression: true
indices.queries.cache.size: 20%
cluster.routing.allocation.same_shard.host: true
bootstrap.memory_lock: false
network.host: 0.0.0.0

discovery.zen.ping.unicast.hosts: ${UNICAST_HOSTS}
discovery.zen.minimum_master_nodes: 2
discovery.zen.fd.ping_interval: 30s
discovery.zen.fd.ping_retries: 6
discovery.zen.fd.ping_timeout: 120s
indices.memory.index_buffer_size: 15%

gateway.recover_after_nodes: 1
bootstrap.system_call_filter: false

xpack.security.enabled: false
xpack.ml.enabled: false

path.data: /data/program/elasticsearch/data
path.logs: /data/program/elasticsearch/logs

