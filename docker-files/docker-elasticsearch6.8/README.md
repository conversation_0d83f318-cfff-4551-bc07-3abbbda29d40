## 说明
1、es 默认开启安全策略导致执行 arthas 失败需要配置 java.policy

2、所以我们自己制作一个es镜像，使用6.8.6版本[es包下载地址](https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-6.8.6.tar.gz)

3、通过 && 将所有的RUN指令合并为一个能减少镜像大小

4、[ES官方Dockerfile](https://github.com/elastic/dockerfiles/blob/v7.17.7/elasticsearch/Dockerfile)

### Build

```
docker build -t elasticsearch:6.8.23 .

docker tag elasticsearch:6.8.23 dockerhub.test.wacai.info/wse/elasticsearch:6.8.23

docker push dockerhub.test.wacai.info/wse/elasticsearch:6.8.23

```

### 调试

修改Dockerfile，最后启动命名修改为：

```
CMD ["/bin/bash"]
```

### Run

```
docker run -it --rm --name es6 elasticsearch:6.8.23
```

### 环境变量替换
```
CLUSTER_NAME=wacaies5
UNICAST_HOSTS=***********,*************
```

## 参考
- [arthas监控elasticsearch（7.x）](https://www.cnblogs.com/liwangcai/p/14060875.html)
- [docker-compose安装elasticsearch集群](https://blog.csdn.net/xixihahalelehehe/article/details/109389780)



