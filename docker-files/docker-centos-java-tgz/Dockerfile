FROM centos:7

ARG JDK_TAR_GZ="jdk-*.tar.gz"
ENV JDK_VERSION 1.8.0_351

#创建用户，ES不能通过root启动
RUN useradd -m -d /home/<USER>/bin/bash appweb

COPY ${JDK_TAR_GZ} .
RUN tar -xvf ${JDK_TAR_GZ} && \
    rm -rf ${JDK_TAR_GZ} && \
    mkdir -p /data/program && \
    mv jdk${JDK_VERSION} /data/program/java && \
    chown -R appweb:appweb /data/program/java

ENV JAVA_HOME /data/program/java
ENV PATH $JAVA_HOME/bin:$PATH
ENV CLASSPATH .:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar

RUN sed -i '<EMAIL>=file:/dev/<EMAIL>=file:/dev/urandom@' \
    $JAVA_HOME/jre/lib/security/java.security