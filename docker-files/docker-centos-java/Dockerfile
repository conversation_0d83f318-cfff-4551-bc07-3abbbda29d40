FROM centos:7

RUN yum -y update
RUN yum -y remove java
RUN yum install -y \
        telnet \
        traceroute \
        iproute \
        iptables \
        net-tools \
        tcpdump \
        tzdata \
        iotop \
        which \
        java-1.8.0-openjdk \
        java-1.8.0-openjdk-devel \
    && rm -f /etc/localtime \
    && ln -sv /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && yum clean all

ENV TZ="Asia/Shanghai"

CMD ["/bin/bash"]       