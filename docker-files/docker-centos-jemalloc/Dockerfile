FROM dockerhub.test.wacai.info/wse/centos-jdk:1.8

RUN yum upgrade -y; yum group install -y "Development Tools"; \
    yum install -y wget tcl which zlib-devel git docbook-xsl libxslt graphviz; \
    yum clean all

ARG JEMALLOC_TAR_GZ="jemalloc-*.tar.gz"
ARG JEMALLOC_DIR="jemalloc-5.3.0"

COPY ${JEMALLOC_TAR_GZ} .

RUN tar -xvf ${JEMALLOC_TAR_GZ} && \
    rm -rf ${JEMALLOC_TAR_GZ} && \
    mv ${JEMALLOC_DIR} /opt/${JEMALLOC_DIR} && \
    mkdir -p /data/program/jeprof/data && \
    mkdir -p /data/program/jeprof/output && \
    mkdir -p /data/program/jeprof/bin 

RUN cd /opt/${JEMALLOC_DIR} && ./autogen.sh --enable-prof
RUN cd /opt/${JEMALLOC_DIR} && make dist
RUN cd /opt/${JEMALLOC_DIR} && make
RUN cd /opt/${JEMALLOC_DIR} && make install

ENV LD_PRELOAD="/usr/local/lib/libjemalloc.so"
ENV MALLOC_CONF="prof_leak:true,prof:true,lg_prof_interval:30,lg_prof_sample:17,prof_prefix:/data/program/jeprof/data/jeprof"

ENV DIAGNOSTIC_DIR /data/program/jeprof/bin
COPY bin/*.sh $DIAGNOSTIC_DIR/
