# 使用 nginx 作为基础镜像
FROM nginx:latest
# 安装 curl 工具
RUN apt-get update && apt-get install -y curl

# 上一个
ARG LAST_APM_VERSION="0.1.3"
# 当前
ARG CUR_APM_VERSION="0.1.4"


# 下载静态文件并复制到目标路径

RUN mkdir -p /usr/share/nginx/html/nexus/service/local/repositories/releases/content/com/wacai/middleware/apm-dist/${LAST_APM_VERSION}
RUN curl -o /usr/share/nginx/html/nexus/service/local/repositories/releases/content/com/wacai/middleware/apm-dist/${LAST_APM_VERSION}/apm-dist-${LAST_APM_VERSION}.tar.gz http://repo.caimi-inc.com/nexus/service/local/repositories/releases/content/com/wacai/middleware/apm-dist/${LAST_APM_VERSION}/apm-dist-${LAST_APM_VERSION}.tar.gz

RUN mkdir -p /usr/share/nginx/html/nexus/service/local/repositories/releases/content/com/wacai/middleware/apm-dist/${CUR_APM_VERSION}
RUN curl -o /usr/share/nginx/html/nexus/service/local/repositories/releases/content/com/wacai/middleware/apm-dist/${CUR_APM_VERSION}/apm-dist-${CUR_APM_VERSION}.tar.gz http://repo.caimi-inc.com/nexus/service/local/repositories/releases/content/com/wacai/middleware/apm-dist/${CUR_APM_VERSION}/apm-dist-${CUR_APM_VERSION}.tar.gz


# 暴露 HTTP 端口
EXPOSE 80

# 设置启动命令
CMD ["nginx", "-g", "daemon off;"]
