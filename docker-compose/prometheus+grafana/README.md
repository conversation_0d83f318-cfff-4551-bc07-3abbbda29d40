参考这篇[丢，我讲的是监控，不是QPS](https://mp.weixin.qq.com/s/q7RisR0252oVGje34KOWXg)这篇文章

新建 prometheus 目录，新增 docker-compose.yml 文件，配置如下:

```yaml
version: '2'

networks:
    monitor:
        driver: bridge

services:
    prometheus:
        image: prom/prometheus
        container_name: prometheus
        hostname: prometheus
        restart: always
        volumes:
            - ./prometheus.yml:/etc/prometheus/prometheus.yml
        ports:
            - "9090:9090"
        networks:
            - monitor

    alertmanager:
        image: prom/alertmanager
        container_name: alertmanager
        hostname: alertmanager
        restart: always
#        volumes:
#            - ./alertmanager.yml:/usr/local/etc/alertmanager.yml
        ports:
            - "9093:9093"
        networks:
            - monitor

    grafana:
        image: grafana/grafana
        container_name: grafana
        hostname: grafana
        restart: always
        ports:
            - "3000:3000"
        networks:
            - monitor

    node-exporter:
        image: quay.io/prometheus/node-exporter
        container_name: node-exporter
        hostname: node-exporter
        restart: always
        ports:
            - "9100:9100"
        networks:
            - monitor

    cadvisor:
        image: google/cadvisor:latest
        container_name: cadvisor
        hostname: cadvisor
        restart: always
        volumes:
            - /:/rootfs:ro
            - /var/run:/var/run:rw
            - /sys:/sys:ro
            - /var/lib/docker/:/var/lib/docker:ro
        ports:
            - "8899:8899"
        networks:
            - monitor
```

这里拉取的镜像分别有：

- `cadvisor` 用于获取docker容器的指标
- `node-exporter` 用户获取服务器的指标
- `grafana` 监控的`web-ui`好用的可视化组件
- `alertmanager` 告警组件（目前暂未用到)
- `prometheus` 核心监控组件



新建prometheus的配置文件`prometheus.yml`

```yaml
global:
  scrape_interval:     15s
  evaluation_interval: 15s
scrape_configs:
  - job_name: 'prometheus'
    static_configs:
    - targets: ['ip:9090']
  - job_name: 'cadvisor'
    static_configs:
    - targets: ['ip:8899']
  - job_name: 'node'
    static_configs:
    - targets: ['ip:9100']  
```

随后在目录下`docker-compose up -d`启动，于是我们就可以分别访问：

- `http://127.0.0.1:9100/metrics`( 查看服务器的指标)
- `http://127.0.0.1:8899/metrics`（查看docker容器的指标）
- `http://127.0.0.1:9090/`(prometheus的原生web-ui)
- `http://127.0.0.1:3000/`(Grafana开源的监控可视化组件页面)



进到Grafana首页，默认密码 admin/admin 。

我们首先要配置prometheus作为我们的数据源

<img width="1127" alt="image" src="https://user-images.githubusercontent.com/1752994/172106450-a8e60d99-b536-428d-8cc0-525abae1d269.png">



进到配置页面，写下对应的URL，然后保存就好了。

配置好数据源之后，我们就可以配置对应的监控信息了，常见的配置监控已经有**对应的模板**了，就不需要我们一个一个地去配置了。（如果不满足的话，那还是得自己去配）

相关的granfa模板可以在 **https://grafana.com/grafana/dashboards/** 这里查到。

我们直接服务器的监控直接选用**8919**的就好了，效果如下：

<img width="1122" alt="image" src="https://user-images.githubusercontent.com/1752994/172106626-75842e25-2277-4f25-ab79-09d573f1c40d.png">

也可以看看Docker的监控（上面启动的`cadvisor`服务就采集了Docker的信息），我们使用模板**893**来配置监控docker的信息：

