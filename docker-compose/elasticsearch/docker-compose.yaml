version: '2.2'
services:
  kibana:
    image: kibana:6.8.6
    container_name: kibana6
    environment:
      - ELASTICSEARCH_HOSTS="http://es6:9200"
      - XPACK_GRAPH_ENABLED=false
      - TIMELION_ENABLED=false
      - XPACK_MONITORING_COLLECTION_ENABLED="false"
      - ELASTICSEARCH_USERNAME=kibana
      - ELASTICSEARCH_PASSWORD=kibana
    ports:
      - "5601:5601"
#    networks:
#      - esnet
    depends_on:
      - es6
  es6:
    image: elasticsearch:6.8.6
    container_name: es6
    hostname: es6
    environment:
      - cluster.name=test-es
      - node.name=es6
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms1G -Xmx1G"
      - "TZ=Asia/Shanghai"
      - discovery.type=single-node
      - path.data=node0_data
      - xpack.security.enabled=false
      - xpack.security.transport.ssl.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    privileged: true
    volumes:
      - ./datadir:/data/program/elasticsearch/data
    ports:
      - 9200:9200
#    networks:
#      - esnet
#networks:
#  esnet:
#    driver: bridge
