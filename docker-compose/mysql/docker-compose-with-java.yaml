version: '3.3'
services:
  mysql:
    image: mysql:5.7
    container_name: mysql-server
    volumes: 
      - ./my.cnf:/etc/my.cnf 
      - ./datadir:/var/lib/mysql
      #- /apps/mysql/datadir:/var/lib/mysql
      #- /apps/mysql/conf/my.cnf:/etc/my.cnf
    environment:
      - "MYSQL_ROOT_PASSWORD=1234"
      - "MYSQL_DATABASE=ucenter"
      - "TZ=Asia/Shanghai"
    ports:
      - 3306:3306
  web:
    image: docker-centos-java:1.8
    container_name: inspector
    command: sh -c "python -m SimpleHTTPServer 8181"
    network_mode: service:mysql
    volumes:
      - ./datadir0:/opt

 # mysql没有tcpdump命令，所以使用另外一个容器来抓包mysql，需要共享同一个网络空间
 # google：docker-compose how to define container scoped network
