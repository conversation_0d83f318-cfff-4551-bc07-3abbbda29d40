version: '3.3'
services:
  mysql:
    image: mysql:5.7.37-debian
    #image: mysql:8.0.30-debian
    container_name: mysql-server
    volumes: 
      - ./my.cnf:/etc/my.cnf 
      - ./datadir:/var/lib/mysql
      #- /apps/mysql/datadir:/var/lib/mysql
      #- /apps/mysql/conf/my.cnf:/etc/my.cnf
    environment:
      - "MYSQL_ROOT_PASSWORD=1234"
      - "MYSQL_DATABASE=ucenter"
      - "TZ=Asia/Shanghai"
    ports:
      - 3306:3306

