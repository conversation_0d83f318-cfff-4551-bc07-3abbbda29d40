version: '3.3'
services:
  elasticsearch:
    image: elasticsearch:7.8.0
    container_name: elasticsearch
    restart: always
    ports:
      - 9200:9200
    environment:
      discovery.type: single-node
      TZ: Asia/Shanghai
    #volumes:
    #   - ./elasticsearch/logs:/usr/share/elasticsearch/logs
    #   - ./elasticsearch/data:/usr/share/elasticsearch/data
    #   - ./elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
    ulimits:
      memlock:
        soft: -1
        hard: -1
  oap:
    image: apache/skywalking-oap-server:8.7.0-es7
    container_name: oap
    depends_on:
      - elasticsearch
    links:
      - elasticsearch
    restart: always
    ports:
      - 11800:11800
      - 12800:12800
    environment:
      SW_STORAGE: elasticsearch7  # 指定ES版本
      SW_STORAGE_ES_CLUSTER_NODES: elasticsearch:9200
      SW_TRACE_SAMPLE_RATE: 5000
      TZ: Asia/Shanghai
    # volumes:
    #  - ./config/alarm-settings.yml:/skywalking/config/alarm-settings.yml
  ui:
    image: apache/skywalking-ui:8.9.1
    container_name: ui
    depends_on:
      - oap
    links:
      - oap
    restart: always
    ports:
      - 8080:8080
    environment:
      SW_OAP_ADDRESS: oap:12800
      TZ: Asia/Shanghai
  kibana: ## 访问地址：http://************:5601/app/kibana
    image: "docker.elastic.co/kibana/kibana:7.8.0"  
    container_name: kibana    
    depends_on:
        - elasticsearch
    ports:
        - "5601:5601"
    links:
        - elasticsearch
    restart: always        
    environment:
        - ELASTICSEARCH_URL=http://elasticsearch:9200
    volumes:
        - /etc/localtime:/etc/localtime

